import { Hono } from 'hono';
import { html } from 'hono/html';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../common/types';
import { DashboardLayoutCard, LayoutWithCard } from '../../ui/components/layout';
import { PlatformTypes } from '../../ui/constants';
import { capitalize } from '../../workflow/utils/helpers';
import { linkValidationMiddleware } from '../middleware/link-validation';
import { USER_ID, LINK_ID, SERVICE_ID, COORDINATOR_STUB } from '../constants';
import { ErrorPageLayout, RetryLimitExceededErrorContent } from '../../ui';

const app = new Hono<KakuApp>();

app.get(`/:${LINK_ID}/:${SERVICE_ID}`, linkValidationMiddleware, async (c) => {
  const userId = c.get(USER_ID);
  const linkId = c.get(LINK_ID);
  const serviceId = c.get(SERVICE_ID);

  const connectionDOName = `${linkId}`;
  const agent = c.env.Connections.idFromName(connectionDOName);
  const stub = c.env.Connections.get(agent);
  await stub.setName(connectionDOName);
  c.executionCtx.waitUntil(
    stub.eagerlyInitializeResources(serviceId as PlatformTypes, userId, linkId),
  );

  return c.html(DashboardLayoutCard(userId, linkId, serviceId, capitalize(serviceId)));
});

app.get(`/:${LINK_ID}/:${SERVICE_ID}/flow`, linkValidationMiddleware, (c) => {
  const userId = c.get(USER_ID);
  const serviceId = c.get(SERVICE_ID);
  const linkId = c.get(LINK_ID);

  const url = `${c.env.KAKU_WS_ENDPOINT}/agents/connections/${linkId}`;
  return c.html(
    LayoutWithCard(
      { wsEndpoint: url, linkId },
      {
        serviceId: serviceId,
        serviceName: capitalize(serviceId),
        serviceLogo: `/fb.png`,
        serviceTitle: 'Testing Forms',
        formTitle: 'Testing Forms',
        serviceDescription: 'Testing Forms',
        liveViewToggleText: 'Show Live View',
        loadingText: 'Processing...',
      },
    ),
  );
});

app.get(`/:${LINK_ID}/:${SERVICE_ID}/reset`, linkValidationMiddleware, async (c) => {
  const userId = c.get(USER_ID);
  const serviceId = c.get(SERVICE_ID);
  const linkId = c.get(LINK_ID);
  const coordinatorStub = c.get(COORDINATOR_STUB);

  try {
    console.log(`[API] Resetting link ${linkId} for platform ${serviceId}`);

    const resetResult = await coordinatorStub.resetLink(linkId, userId);

    if (!resetResult) {
      return c.html(`<h1>Link not found or not active</h1>`, 404);
    }

    console.log(`[API] Link reset successful, new agent ID: ${resetResult.newAgentId}`);

    const { linkInfo } = resetResult;

    // Redirect to the updated link URL
    return c.redirect(linkInfo.url);
  } catch (error) {
    console.error(`[API] Error resetting link ${linkId}:`, error);

    if (error instanceof Error && error.message.includes('Retry limit exceeded')) {
      return c.html(
        ErrorPageLayout({
          title: 'Retry Limit Exceeded',
          children: RetryLimitExceededErrorContent(),
        }),
        429,
      );
    }

    return c.html(
      `<h1>Error resetting link: ${error instanceof Error ? error.message : 'Unknown error'}</h1>`,
      500,
    );
  }
});

export { app as ConnectionsHandler };
