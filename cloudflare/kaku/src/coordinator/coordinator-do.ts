import { Agent, AgentContext } from 'agents';
import { PlatformTypes } from '../ui/constants';
import {
  CoordinatorState,
  PlatformMetadata,
  LinkInfo,
  CoordinatorUtils,
  COORDINATOR_CONSTANTS,
  CreateLinkResponse,
} from '../shared/coordinator-types';

/**
 * CoordinatorDO - Manages links and Agent DOs for platform connections
 *
 * This Agent serves as a central coordinator for managing
 * links, Agent DO lifecycle, and platform-specific retry logic.
 */
export class CoordinatorDO extends Agent<Env, CoordinatorState> {
  initialState: CoordinatorState = {
    platforms: [],
  };

  constructor(ctx: AgentContext, env: Env) {
    super(ctx, env);
  }

  /**
   * Get or create platform metadata
   */
  private getOrCreatePlatform(platformId: PlatformTypes): PlatformMetadata {
    let platform = this.state.platforms.find((p) => p.id === platformId);

    if (!platform) {
      platform = {
        id: platformId,
        links: [],
        connected: false,
        retryCount: 0,
      };

      this.setState({
        platforms: [...this.state.platforms, platform],
      });
    }

    return platform;
  }

  // RPC Methods for link management

  /**
   * Create a new link for platform connection
   */
  async createLink(platform: PlatformTypes, userId: string): Promise<CreateLinkResponse> {
    const platformData = this.getOrCreatePlatform(platform);

    if (platformData.connected) {
      throw new Error(`User is already connected to ${platform}`);
    }

    // Check if retries are exhausted for this platform
    if (!CoordinatorUtils.canRetry(platformData.retryCount)) {
      throw new Error(`Retry limit exceeded for ${platform}`);
    }

    const { newLinkId, newAgentId } = await this.setupNewLink(platformData, userId);

    const url = `${this.env.KAKU_API_ENDPOINT}/connections/${newLinkId}/${platform}?userId=${userId}`;
    const now = Date.now();
    const expiresAt = now + COORDINATOR_CONSTANTS.LINK_EXPIRATION_TIME;

    const linkInfo: LinkInfo = {
      status: 'active',
      url,
      createdAt: now,
      expiresAt,
      agentId: newAgentId,
    };

    const updatedPlatforms = this.state.platforms.map((p) =>
      p.id === platform ? { ...p, links: [...p.links, linkInfo] } : p,
    );

    this.setState({
      platforms: updatedPlatforms,
    });

    await this.schedule(new Date(expiresAt), 'handleLinkExpiration', newLinkId);

    return { linkId: newLinkId, url, expiresAt };
  }

  /**
   * Get status of a specific link
   */
  getStatus(linkId: string): LinkInfo | null {
    for (const platform of this.state.platforms) {
      const link = platform.links.find((l) => l.url.includes(linkId));
      if (link) {
        if (Date.now() > link.expiresAt && link.status === 'active') {
          this.expireLink(linkId);
          return { ...link, status: 'expired' };
        }
        return link;
      }
    }
    return null;
  }

  /**
   * Handle link expiration (called by scheduler)
   */
  async handleLinkExpiration(linkId: string): Promise<void> {
    console.log(`[CoordinatorDO] Handling expiration for link: ${linkId}`);
    this.expireLink(linkId);

    // Schedule deletion after 7 days
    const deletionTime = Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY;
    await this.schedule(new Date(deletionTime), 'handleLinkDeletion', linkId);
  }

  /**
   * Handle link deletion (called by scheduler)
   */
  async handleLinkDeletion(linkId: string): Promise<void> {
    const agentId = this.getAgentIdForLink(linkId);
    if (!agentId) {
      throw new Error(`Agent ID not found for link: ${linkId}`);
    }
    await this.deleteAgentDO(agentId);
  }

  // Private helper methods

  /**
   * Create a new Agent DO for the link
   */
  private async createAgentDO(
    linkId: string,
    platform: PlatformTypes,
    userId: string,
  ): Promise<string> {
    const agentId = linkId;

    const agentStub = this.env.Connections.idFromName(agentId);
    const agent = this.env.Connections.get(agentStub);
    agent.setup(platform, userId, linkId);
    console.log(`[CoordinatorDO] Created Agent DO: ${agentId} for platform: ${platform}`);
    await this.schedule(
      new Date(Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY),
      'deleteAgentDO',
      agentId,
    );
    return agentId;
  }

  /**
   * Delete an Agent DO
   */
  async deleteAgentDO(agentId: string): Promise<void> {
    try {
      const agentStub = this.env.Connections.idFromName(agentId);
      const agent = this.env.Connections.get(agentStub);

      // Delete all data from the Agent DO
      await agent.deleteAll();
    } catch (error) {
      console.error(`[CoordinatorDO] Failed to delete Agent DO ${agentId}:`, error);
    }
  }

  /**
   * Expire a link and schedule deletion
   */
  private expireLink(linkId: string): void {
    // Find and update the link status across all platforms
    const updatedPlatforms = this.state.platforms.map((platform) => ({
      ...platform,
      links: platform.links.map((link) =>
        link.url.includes(linkId) ? { ...link, status: 'expired' as const } : link,
      ),
    }));

    this.setState({
      platforms: updatedPlatforms,
    });
  }

  /**
   * Reset a link by creating a new Agent DO (retry functionality)
   */
  async resetLink(
    linkId: string,
    userId: string,
  ): Promise<{ newAgentId: string; linkInfo: LinkInfo } | null> {
    let targetPlatform: PlatformMetadata | null = null;
    let targetLink: LinkInfo | null = null;

    for (const platform of this.state.platforms) {
      const link = platform.links.find((l) => l.url.includes(linkId));
      if (link) {
        targetPlatform = platform;
        targetLink = link;
        break;
      }
    }

    if (!targetPlatform || !targetLink || targetLink.status !== 'active') {
      return null;
    }

    if (!CoordinatorUtils.canRetry(targetPlatform.retryCount)) {
      throw new Error(`Retry limit exceeded for ${targetPlatform.id}`);
    }

    const {
      newLinkInfo,
      newLinkId,
      newAgentId,
    }: { newLinkInfo: LinkInfo; newLinkId: string; newAgentId: string } = await this.setupNewLink(
      targetPlatform,
      userId,
    );

    const updatedPlatforms = this.state.platforms.map((platform) => {
      if (platform.id === targetPlatform!.id) {
        return {
          ...platform,
          retryCount: platform.retryCount + 1,
          links: platform.links
            .map((link) =>
              link.url.includes(linkId) ? { ...link, status: 'expired' as const } : link,
            )
            .concat([newLinkInfo]),
        };
      }
      return platform;
    });

    this.setState({
      platforms: updatedPlatforms,
    });

    await this.schedule(new Date(newLinkInfo.expiresAt), 'handleLinkExpiration', newLinkId);

    console.log(
      `[CoordinatorDO] Reset link ${linkId} with new link ${newLinkId} and Agent DO: ${newAgentId}`,
    );
    return { newAgentId, linkInfo: newLinkInfo };
  }

  private async setupNewLink(targetPlatform: PlatformMetadata, userId: string) {
    const newLinkId = CoordinatorUtils.generateLinkId();
    const url = `${this.env.KAKU_API_ENDPOINT}/connections/${newLinkId}/${targetPlatform.id}?userId=${userId}`;
    const now = Date.now();
    const expiresAt = now + COORDINATOR_CONSTANTS.LINK_EXPIRATION_TIME;

    const newAgentId = await this.createAgentDO(newLinkId, targetPlatform.id, userId);

    const newLinkInfo: LinkInfo = {
      status: 'active',
      url,
      createdAt: now,
      expiresAt,
      agentId: newAgentId,
    };
    return { newLinkInfo, newLinkId, newAgentId };
  }

  /**
   * Mark a platform as connected for this user
   */
  async markPlatformConnected(linkId: string): Promise<void> {
    const updatedPlatforms = this.state.platforms.map((platform) => {
      const linkIndex = platform.links.findIndex((l) => l.url.includes(linkId));
      if (linkIndex !== -1) {
        const updatedLinks = [...platform.links];
        updatedLinks[linkIndex] = {
          ...updatedLinks[linkIndex],
          status: 'connected',
          connectedAt: Date.now(),
        };

        return {
          ...platform,
          connected: true,
          links: updatedLinks,
        };
      }
      return platform;
    });

    this.setState({
      platforms: updatedPlatforms,
    });

    // Schedule deletion right away for connected links (7 days)
    const deletionTime = Date.now() + COORDINATOR_CONSTANTS.AGENT_DELETION_DELAY;
    await this.schedule(new Date(deletionTime), 'handleLinkDeletion', linkId);
  }

  /**
   * Get Agent DO identifier for a link
   */
  getAgentIdForLink(linkId: string): string | null {
    // Find the link across all platforms
    for (const platform of this.state.platforms) {
      const link = platform.links.find((l) => l.url.includes(linkId));
      if (link) {
        return link.agentId || null;
      }
    }
    return null;
  }
}
