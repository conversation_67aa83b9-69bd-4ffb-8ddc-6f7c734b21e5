import { env } from 'cloudflare:test';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { CoordinatorDO } from '../../src/coordinator/coordinator-do';
import { PlatformTypes } from '../../src/ui/constants';
import app from '../../src/api/index';
import { CreateLinkResponse } from '../../src/shared/coordinator-types';

describe('API Endpoints Integration Tests', () => {
  let testCounter = 0;

  beforeEach(async () => {
    testCounter++;
  });

  describe('POST /links', () => {
    it('should create a new link successfully', async () => {
      // Arrange
      const platform: PlatformTypes = 'facebook';
      const userId = 'test-user-create';
      const requestBody = {
        serviceId: platform,
        userId: userId,
      };

      // Act
      const response = await app.request('/links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      }, env);

      // Assert
      expect(response.status).toBe(200);

      const responseData = await response.json() as CreateLinkResponse;
      expect(responseData).toBeDefined();
      expect(responseData.linkId).toBeDefined();
      expect(responseData.url).toBeDefined();
      expect(responseData.expiresAt).toBeDefined();

      // Verify URL format
      expect(responseData.url).toContain(responseData.linkId);
      expect(responseData.url).toContain(platform);
      expect(responseData.url).toContain(`userId=${userId}`);
    });

    it('should return 400 for invalid serviceId', async () => {
      // Arrange
      const requestBody = {
        serviceId: 'invalid-platform',
        userId: 'test-user',
      };

      // Act
      const response = await app.request('/links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      }, env);

      // Assert
      expect(response.status).toBe(400);

      const responseData = await response.json() as any;
      expect(responseData.error).toBe('Invalid serviceId');
    });
  });

  describe('GET /:linkId/:serviceId/reset', () => {
    it('should reset a link successfully', async () => {
      // Arrange - Create a link first
      const serviceId: PlatformTypes = 'netflix';
      const userId = `test-user-reset-${testCounter}`;

      const coordinatorId = env.CoordinatorDO.idFromName(userId);
      const coordinator = env.CoordinatorDO.get(coordinatorId) as DurableObjectStub<CoordinatorDO>;

      const createResult = await coordinator.createLink(serviceId, userId);
      const linkId = createResult.linkId;

      // Act
      const response = await app.request(`/connections/${linkId}/${serviceId}/reset?userId=${userId}`, {
        method: 'GET',
      }, env);

      // Assert
      expect(response.status).toBe(302); // Redirect status

      // Check that the location header contains a new URL
      const location = response.headers.get('location');
      expect(location).toBeDefined();
      expect(location).toContain(serviceId);

      // Verify the original link is now expired
      const linkStatus = await coordinator.getStatus(linkId);
      expect(linkStatus?.status).toBe('expired');
    });

    it('should return 500 for non-existent link (invalid link middleware)', async () => {
      // Arrange
      const nonExistentLinkId = 'non-existent-link-id';
      const serviceId = 'facebook';
      const userId = 'test-user';

      // Act
      const response = await app.request(`/connections/${nonExistentLinkId}/${serviceId}/reset?userId=${userId}`, {
        method: 'GET',
      }, env);

      // Assert
      expect(response.status).toBe(500);

      const responseText = await response.text();
      expect(responseText).toContain('Invalid Link');
    });

    it('should handle retry limit exceeded error', async () => {
      // Arrange
      const serviceId: PlatformTypes = 'github';
      const userId = `test-user-retry-${testCounter}`;

      const coordinatorId = env.CoordinatorDO.idFromName(userId);
      const coordinator = env.CoordinatorDO.get(coordinatorId) as DurableObjectStub<CoordinatorDO>;

      const createResult = await coordinator.createLink(serviceId, userId);
      let currentLinkId = createResult.linkId;

      // retry attempts (PLATFORM_RETRY_LIMIT = 3)
      for (let i = 0; i < 3; i++) {
        const resetResult = await coordinator.resetLink(currentLinkId, userId);
        if (resetResult) {
          currentLinkId = resetResult.linkInfo.url.split('/')[4];
        }
      }

      // Act - Try to reset again (should exceed limit)
      const response = await app.request(`/connections/${currentLinkId}/${serviceId}/reset?userId=${userId}`, {
        method: 'GET',
      }, env);

      // Assert
      expect(response.status).toBe(429); // Too Many Requests

      const responseText = await response.text();
      expect(responseText).toContain('Maximum Retry Attempts Reached');
      expect(responseText).toContain('retry attempts (3)');
    });
  });
});
