<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTMX Form Generator Test - Welcome</title>
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script>
    <link rel="stylesheet" href="../../public/css/styles.css" />
    <link rel="stylesheet" href="../../public/css/video-layout.css" />
    <link rel="stylesheet" href="../../public/css/bottom-sheet.css" />
</head>
<body class="light bg-neutral-60 dark:bg-primary-50 h-screen flex items-center justify-center">
    <div class="relative md:w-[435px] md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
        <div id="connection-flow-wrapper" class="h-screen flex flex-col justify-center items-center relative md:w-[435px] md:h-auto md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
            <div id="connection-flow" class="w-screen flex flex-col items-center justify-center bg-surface text-surface-on-surface md:h-[auto] md:min-h-[600px] md:w-[435px] md:rounded-xl p-8">
                <div class="text-start w-full">
                    <h1 class="text-xl font-semibold mb-4">Welcome</h1>
                    <p class="text-base mb-2">Enter your password</p>
                </div>
                <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-vals='{"type": "form_submission"}'>
<div class="form-error">Wrong password. Try again or click Forgot password to reset it.</div>

    <div class="input-container">
      <div class="floating-input-wrapper">
        <input
          class="floating-input"
          name="password"
          type="password"
          placeholder=""
          id="password_field"
        >
        <label class="floating-label" for="password_field">Enter your password</label>
      </div>
    </div>
  

    <div class="input-container">
      <div class="checkbox-container">
        <input
          class="checkbox-field"
          type="checkbox"
          id="show_password_checkbox"
          name="show_password"
        >
        <label class="checkbox-label" for="show_password_checkbox">Show password</label>
      </div>
    </div>
  

    <div class="button-container">
      <button
        type="button"
        class="button-link"
        hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-trigger="click" hx-vals='{"type": "form_submission", "clickId": "forgot_password_button", "interaction": "click"}'
      >
        Forgot password?
      </button>
    </div>
  

    <div class="button-container">
      <button
        type="submit"
        class="button-primary"
        hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-vals='{"type": "form_submission", "clickId": "next_button", "interaction": "submit"}'
      >
        Next
      </button>
    </div>
  
</form>

               <div style="margin-top: 2rem; padding: 1rem; background-color: #f0f0f0; border-radius: 0.5rem; width: 100%;">
  <h3 style="font-size: 0.875rem; font-weight: 600; color: #1c1b1f; margin-bottom: 0.5rem;">
    Form Analysis Data (Simplified Schema):
  </h3>
  <details style="font-size: 0.75rem;">
    <summary style="cursor: pointer; color: #6750a4; margin-bottom: 0.5rem; transition: color 0.2s;"
             onmouseover="this.style.color='#5f4da5'"
             onmouseout="this.style.color='#6750a4'">
      View Raw JSON
    </summary>
    <pre style="margin-top: 0.5rem; padding: 0.5rem; background-color: #ffffff; border: 1px solid #cac4d0; border-radius: 0.25rem; font-size: 0.75rem; overflow: auto; max-height: 10rem; color: #1c1b1f;">
      {
  "extractionResult": {
    "screenInfo": {
      "authState": "not-authenticated",
      "errors": [
        "Wrong password. Try again or click Forgot password to reset it."
      ],
      "title": "Welcome",
      "controlVisibilityRules": [
        {
          "id": "password_field",
          "status": "included",
          "reason": "Password input field for authentication."
        },
        {
          "id": "show_password_checkbox",
          "status": "included",
          "reason": "Checkbox to toggle password visibility."
        },
        {
          "id": "forgot_password_button",
          "status": "included",
          "reason": "Button to initiate password recovery."
        },
        {
          "id": "next_button",
          "status": "included",
          "reason": "Primary button to submit credentials."
        },
        {
          "id": "account_selection_dropdown",
          "status": "excluded",
          "reason": "Account selection is not part of the core authentication flow."
        }
      ],
      "description": "Welcome",
      "instruction": "Enter your password"
    },
    "controls": {
      "fields": [
        {
          "id": "password_field",
          "order": 1,
          "label": "Enter your password",
          "type": "password",
          "actiontype": "fill",
          "name": "password",
          "checked": false
        },
        {
          "id": "show_password_checkbox",
          "order": 2,
          "label": "Show password",
          "type": "checkbox",
          "actiontype": "select",
          "name": "show_password",
          "checked": false
        }
      ],
      "buttons": [
        {
          "id": "forgot_password_button",
          "order": 3,
          "label": "Forgot password?",
          "variant": "link",
          "type": "click",
          "actiontype": "click"
        },
        {
          "id": "next_button",
          "order": 4,
          "label": "Next",
          "variant": "primary",
          "type": "submit",
          "actiontype": "click"
        }
      ]
    }
  },
  "classificationResult": {
    "screenInfo": {
      "classificationReasoning": "The screen displays a password input field and an error message indicating a wrong password, which is typical for a login attempt.",
      "screenClass": "other",
      "description": "Enter your password",
      "title": "Welcome",
      "authState": "not-authenticated",
      "errors": [
        "Wrong password. Try again or click Forgot password to reset it."
      ],
      "verificationCode": null
    }
  }
}
    </pre>
  </details>
</div>

            </div>
        </div>
    </div>
</body>
</html>